import { StudentResponse } from "@/lib/server/types/student/student"
import { DashContentPagination, DashContentPaginationItem } from "../DashCrudContent"
import { getStudents } from "@/lib/server/actions/student/getStudents";

interface StudentPaginationProps {
    data: StudentResponse;
    currentPage: number;
    search: string;
    year?: string;
}

export default async function StudentPagination({ currentPage, search, year }: StudentPaginationProps) {
    const data = await getStudents(currentPage, search, year);

    const buildUrl = (page: number) => {
        const params = new URLSearchParams();
        params.set('page', page.toString());
        if (search) params.set('search', search);
        if (year) params.set('year', year);
        return `/dashboard/students?${params.toString()}`;
    };

    return (
        <DashContentPagination>
            {/* Previous button */}
            {data.prev_page_url && (
                <DashContentPaginationItem
                    href={buildUrl(currentPage - 1)}
                >
                    Previous
                </DashContentPaginationItem>
            )}

            {/* Page numbers */}
            {data.links.slice(1, -1).map((link, index) => (
                link.url && (
                    <DashContentPaginationItem
                        key={index}
                        href={buildUrl(index + 1)}
                    >
                        {link.label}
                    </DashContentPaginationItem>
                )
            ))}

            {/* Next button */}
            {data.next_page_url && (
                <DashContentPaginationItem
                    href={buildUrl(currentPage + 1)}
                >
                    Next
                </DashContentPaginationItem>
            )}
        </DashContentPagination>
    )
}