'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { StudentResponse } from '../../types/student/student'

export async function getStudents(page: number = 1, search: string = '', year?: string): Promise<StudentResponse> {
    try {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        if (search) {
            params.append('search', search);
        }
        if (year) {
            params.append('year', year);
        }
        const { data } = await axiosInstance.get<StudentResponse>(`/students?${params.toString()}`)
        return data
    } catch (error: any) {
        console.error('Error fetching students:', error.response?.data)
        throw error.response?.data
    }
}