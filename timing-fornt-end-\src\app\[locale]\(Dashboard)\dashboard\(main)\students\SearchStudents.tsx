"use client";

import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { useForm } from "react-hook-form";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { useRouter, useSearchParams } from "next/navigation";
import { Search } from "lucide-react";
import { useEffect, useState } from "react";
import { getYears } from "@/lib/server/actions/year/yearActions";

interface SearchFormData {
    search: string;
    year: string;
}

interface Year {
    id: number;
    name: string;
    department: {
        id: number;
        name: string;
    };
}

export default function SearchStudents() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [years, setYears] = useState<Year[]>([]);
    const { register, handleSubmit, watch } = useForm<SearchFormData>({
        defaultValues: {
            search: searchParams.get("search") || "",
            year: searchParams.get("year") || "",
        },
    });

    useEffect(() => {
        const fetchYears = async () => {
            try {
                const yearsData = await getYears();
                setYears(yearsData.data || []);
            } catch (error) {
                console.error('Error fetching years:', error);
            }
        };
        fetchYears();
    }, []);

    const onSubmit = (data: SearchFormData) => {
        const params = new URLSearchParams(searchParams.toString());
        if (data.search) {
            params.set("search", data.search);
        } else {
            params.delete("search");
        }
        if (data.year) {
            params.set("year", data.year);
        } else {
            params.delete("year");
        }
        params.set("page", "1"); // Reset to first page when searching
        router.push(`/dashboard/students?${params.toString()}`);
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex items-center gap-2">
            <div className="flex items-center gap-2">
                <label htmlFor="year" className="font-medium text-sm">Year:</label>
                <select
                    id="year"
                    {...register("year")}
                    className="border rounded px-2 py-1 text-sm"
                >
                    <option value="">All Years</option>
                    {years.map(year => (
                        <option key={year.id} value={year.id}>
                            {year.name} ({year.department.name})
                        </option>
                    ))}
                </select>
            </div>
            <Input
                type="text"
                placeholder="Search students..."
                label="search"
                title=""
                register={register}
            />
            <Button type="submit" mode="filled" icon={<Search />}>
                Search
            </Button>
        </form>
    );
}